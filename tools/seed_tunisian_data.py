#!/usr/bin/env python3
"""
Database seeding script for Tunisian prep school data.
This script clears the database and populates it with realistic Tunisian prep school data.
"""

import sys
import os
import sqlite3
from datetime import datetime
import json

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from facial_recognition_system.config import Config
from gui.services.auth_service import AuthService
from facial_recognition_system.local_database import initialize_database
from quiz_management.services.quiz_service import initialize_quiz_tables

def clear_database():
    """Clear all data from the database while preserving structure."""
    print("🗑️  Clearing database...")
    
    try:
        Config.ensure_directories()
        db_path = Config.get_db_path()
        
        with sqlite3.connect(db_path) as conn:
            conn.execute('PRAGMA foreign_keys = OFF')
            cursor = conn.cursor()
            
            # Get all table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            # Clear all tables
            for table in tables:
                table_name = table[0]
                if table_name != 'sqlite_sequence':  # Don't clear the sequence table
                    cursor.execute(f"DELETE FROM {table_name}")
                    print(f"   ✅ Cleared table: {table_name}")
            
            # Reset auto-increment sequences
            cursor.execute("DELETE FROM sqlite_sequence")
            
            conn.execute('PRAGMA foreign_keys = ON')
            conn.commit()
            
        print("✅ Database cleared successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to clear database: {e}")
        return False

def create_teachers():
    """Create Tunisian teacher accounts."""
    print("👨‍🏫 Creating teacher accounts...")
    
    auth_service = AuthService()
    
    # Tunisian teacher data
    teachers_data = [
        {
            "username": "ahmed_ben_salem",
            "password": "teacher123",
            "full_name": "Ahmed Ben Salem",
            "email": "<EMAIL>",
            "phone": "+216 98 123 456"
        },
        {
            "username": "fatma_trabelsi",
            "password": "teacher123", 
            "full_name": "Fatma Trabelsi",
            "email": "<EMAIL>",
            "phone": "+216 97 234 567"
        },
        {
            "username": "mohamed_karray",
            "password": "teacher123",
            "full_name": "Mohamed Karray", 
            "email": "<EMAIL>",
            "phone": "+216 96 345 678"
        },
        {
            "username": "leila_bouazizi",
            "password": "teacher123",
            "full_name": "Leila Bouazizi",
            "email": "<EMAIL>", 
            "phone": "+216 95 456 789"
        },
        {
            "username": "karim_nasri",
            "password": "teacher123",
            "full_name": "Karim Nasri",
            "email": "<EMAIL>",
            "phone": "+216 94 567 890"
        }
    ]
    
    created_teachers = []
    
    for teacher_data in teachers_data:
        success = auth_service.create_teacher(
            username=teacher_data["username"],
            password=teacher_data["password"],
            full_name=teacher_data["full_name"],
            email=teacher_data["email"],
            phone=teacher_data["phone"]
        )
        
        if success:
            print(f"   ✅ Created teacher: {teacher_data['full_name']}")
            created_teachers.append(teacher_data)
        else:
            print(f"   ❌ Failed to create teacher: {teacher_data['full_name']}")
    
    return created_teachers

def get_teacher_ids():
    """Get teacher IDs from the database."""
    try:
        db_path = Config.get_db_path()
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT id, username FROM users WHERE role = 'teacher'")
            teachers = cursor.fetchall()
            
            return {teacher['username']: teacher['id'] for teacher in teachers}
            
    except Exception as e:
        print(f"❌ Failed to get teacher IDs: {e}")
        return {}

def create_classes_and_subjects():
    """Create Tunisian prep school classes and subjects."""
    print("🏫 Creating classes and subjects...")
    
    teacher_ids = get_teacher_ids()
    if not teacher_ids:
        print("❌ No teachers found. Cannot create classes.")
        return False
    
    # Tunisian prep school structure
    classes_data = [
        {
            "name": "1ère Sciences Expérimentales A",
            "description": "Première année Sciences Expérimentales section A",
            "teacher": "ahmed_ben_salem",
            "subjects": ["Mathématiques", "Physique", "Chimie", "Sciences Naturelles", "Français", "Arabe", "Anglais"]
        },
        {
            "name": "1ère Sciences Expérimentales B", 
            "description": "Première année Sciences Expérimentales section B",
            "teacher": "fatma_trabelsi",
            "subjects": ["Mathématiques", "Physique", "Chimie", "Sciences Naturelles", "Français", "Arabe", "Anglais"]
        },
        {
            "name": "1ère Mathématiques A",
            "description": "Première année Mathématiques section A", 
            "teacher": "mohamed_karray",
            "subjects": ["Mathématiques", "Physique", "Sciences Naturelles", "Français", "Arabe", "Anglais", "Philosophie"]
        },
        {
            "name": "1ère Mathématiques B",
            "description": "Première année Mathématiques section B",
            "teacher": "leila_bouazizi", 
            "subjects": ["Mathématiques", "Physique", "Sciences Naturelles", "Français", "Arabe", "Anglais", "Philosophie"]
        },
        {
            "name": "1ère Lettres",
            "description": "Première année Lettres",
            "teacher": "karim_nasri",
            "subjects": ["Français", "Arabe", "Anglais", "Philosophie", "Histoire-Géographie", "Mathématiques"]
        }
    ]
    
    try:
        db_path = Config.get_db_path()
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            now = datetime.now().isoformat()
            
            for class_data in classes_data:
                teacher_id = teacher_ids.get(class_data["teacher"])
                if not teacher_id:
                    print(f"   ❌ Teacher not found: {class_data['teacher']}")
                    continue
                
                # Create class
                cursor.execute("""
                    INSERT INTO classes (name, description, teacher_id, created_at)
                    VALUES (?, ?, ?, ?)
                """, (class_data["name"], class_data["description"], teacher_id, now))
                
                class_id = cursor.lastrowid
                print(f"   ✅ Created class: {class_data['name']}")
                
                # Create subjects for this class
                for subject_name in class_data["subjects"]:
                    cursor.execute("""
                        INSERT INTO matieres (name, class_id, teacher_id, created_at)
                        VALUES (?, ?, ?, ?)
                    """, (subject_name, class_id, teacher_id, now))
                    
                    print(f"      ✅ Created subject: {subject_name}")
            
            conn.commit()
            
        print("✅ Classes and subjects created successfully")
        return True

    except Exception as e:
        print(f"❌ Failed to create classes and subjects: {e}")
        return False

def create_sample_students():
    """Create sample Tunisian students with dummy face encodings."""
    print("👨‍🎓 Creating sample students...")

    # Tunisian student names
    students_data = [
        # 1ère Sciences Expérimentales A
        {"name": "Youssef Ben Ahmed", "class": "1ère Sciences Expérimentales A"},
        {"name": "Amira Trabelsi", "class": "1ère Sciences Expérimentales A"},
        {"name": "Mehdi Karray", "class": "1ère Sciences Expérimentales A"},
        {"name": "Sarra Bouazizi", "class": "1ère Sciences Expérimentales A"},
        {"name": "Omar Nasri", "class": "1ère Sciences Expérimentales A"},
        {"name": "Ines Ben Salem", "class": "1ère Sciences Expérimentales A"},
        {"name": "Khalil Jemli", "class": "1ère Sciences Expérimentales A"},
        {"name": "Nour Hamdi", "class": "1ère Sciences Expérimentales A"},

        # 1ère Sciences Expérimentales B
        {"name": "Rami Cherni", "class": "1ère Sciences Expérimentales B"},
        {"name": "Yasmine Dridi", "class": "1ère Sciences Expérimentales B"},
        {"name": "Fares Mejri", "class": "1ère Sciences Expérimentales B"},
        {"name": "Mariem Gharbi", "class": "1ère Sciences Expérimentales B"},
        {"name": "Aziz Belhaj", "class": "1ère Sciences Expérimentales B"},
        {"name": "Rim Ouali", "class": "1ère Sciences Expérimentales B"},
        {"name": "Wassim Zouari", "class": "1ère Sciences Expérimentales B"},
        {"name": "Dorra Mansouri", "class": "1ère Sciences Expérimentales B"},

        # 1ère Mathématiques A
        {"name": "Seif Eddine Kacem", "class": "1ère Mathématiques A"},
        {"name": "Chaima Bouzid", "class": "1ère Mathématiques A"},
        {"name": "Aymen Tlili", "class": "1ère Mathématiques A"},
        {"name": "Malek Agrebi", "class": "1ère Mathématiques A"},
        {"name": "Nadia Ferchichi", "class": "1ère Mathématiques A"},
        {"name": "Hedi Maalej", "class": "1ère Mathématiques A"},
        {"name": "Salma Rekik", "class": "1ère Mathématiques A"},
        {"name": "Bilel Hajji", "class": "1ère Mathématiques A"},

        # 1ère Mathématiques B
        {"name": "Emna Sellami", "class": "1ère Mathématiques B"},
        {"name": "Montassar Ghanmi", "class": "1ère Mathématiques B"},
        {"name": "Lina Cherif", "class": "1ère Mathématiques B"},
        {"name": "Houssem Baccouche", "class": "1ère Mathématiques B"},
        {"name": "Nesrine Abidi", "class": "1ère Mathématiques B"},
        {"name": "Chaker Brahim", "class": "1ère Mathématiques B"},
        {"name": "Wafa Jrad", "class": "1ère Mathématiques B"},
        {"name": "Tarek Mhiri", "class": "1ère Mathématiques B"},

        # 1ère Lettres
        {"name": "Aya Khelifi", "class": "1ère Lettres"},
        {"name": "Sami Bouslama", "class": "1ère Lettres"},
        {"name": "Meriem Daoud", "class": "1ère Lettres"},
        {"name": "Riadh Zoghlami", "class": "1ère Lettres"},
        {"name": "Siwar Benaissa", "class": "1ère Lettres"},
        {"name": "Hatem Jlassi", "class": "1ère Lettres"},
        {"name": "Olfa Mzoughi", "class": "1ère Lettres"},
        {"name": "Kamel Baccari", "class": "1ère Lettres"}
    ]

    try:
        db_path = Config.get_db_path()
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get class IDs
            cursor.execute("SELECT id, name FROM classes")
            classes = {row['name']: row['id'] for row in cursor.fetchall()}

            now = datetime.now().isoformat()

            for student in students_data:
                class_id = classes.get(student["class"])
                if not class_id:
                    print(f"   ❌ Class not found: {student['class']}")
                    continue

                # Create dummy face encoding (128-dimensional vector)
                import random
                dummy_encoding = [random.uniform(-1, 1) for _ in range(128)]
                encoded_data = json.dumps(dummy_encoding)

                cursor.execute("""
                    INSERT INTO etudiants (name, code, class_id, created_at)
                    VALUES (?, ?, ?, ?)
                """, (student["name"], encoded_data, class_id, now))

                print(f"   ✅ Created student: {student['name']} in {student['class']}")

            conn.commit()

        print("✅ Sample students created successfully")
        return True

    except Exception as e:
        print(f"❌ Failed to create students: {e}")
        return False

def create_sample_quizzes():
    """Create sample quizzes for different subjects."""
    print("📝 Creating sample quizzes...")

    try:
        db_path = Config.get_db_path()
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get classes, subjects, and teachers
            cursor.execute("""
                SELECT c.id as class_id, c.name as class_name, c.teacher_id,
                       m.id as subject_id, m.name as subject_name
                FROM classes c
                JOIN matieres m ON c.id = m.class_id
                WHERE m.name IN ('Mathématiques', 'Physique', 'Français', 'Arabe')
                LIMIT 10
            """)

            subjects = cursor.fetchall()
            now = datetime.now().isoformat()

            quiz_templates = [
                {
                    "title": "Quiz Mathématiques - Fonctions",
                    "description": "Évaluation sur les fonctions mathématiques",
                    "subject": "Mathématiques"
                },
                {
                    "title": "Quiz Physique - Mécanique",
                    "description": "Test sur les principes de la mécanique",
                    "subject": "Physique"
                },
                {
                    "title": "Quiz Français - Grammaire",
                    "description": "Évaluation de grammaire française",
                    "subject": "Français"
                },
                {
                    "title": "Quiz Arabe - النحو",
                    "description": "اختبار في قواعد النحو العربي",
                    "subject": "Arabe"
                }
            ]

            for subject in subjects:
                for template in quiz_templates:
                    if template["subject"] == subject["subject_name"]:
                        cursor.execute("""
                            INSERT INTO quiz (title, description, class_id, subject_id, teacher_id, created_at)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (
                            template["title"],
                            template["description"],
                            subject["class_id"],
                            subject["subject_id"],
                            subject["teacher_id"],
                            now
                        ))

                        print(f"   ✅ Created quiz: {template['title']} for {subject['class_name']}")

            conn.commit()

        print("✅ Sample quizzes created successfully")
        return True

    except Exception as e:
        print(f"❌ Failed to create quizzes: {e}")
        return False

def create_attendance_history():
    """Create sample attendance history for students."""
    print("📊 Creating attendance history...")

    try:
        db_path = Config.get_db_path()
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get all students with their class and subject information
            cursor.execute("""
                SELECT e.id as student_id, e.name as student_name,
                       c.id as class_id, c.name as class_name,
                       m.id as subject_id, m.name as subject_name
                FROM etudiants e
                JOIN classes c ON e.class_id = c.id
                JOIN matieres m ON c.id = m.class_id
                ORDER BY e.id, m.id
            """)

            student_subjects = cursor.fetchall()

            # Generate attendance for the last 30 days
            from datetime import datetime, timedelta
            import random

            base_date = datetime.now()
            attendance_records = []

            for days_ago in range(30):
                current_date = base_date - timedelta(days=days_ago)
                date_str = current_date.strftime("%Y-%m-%d")
                time_str = "08:00:00"  # Morning session
                timestamp = current_date.isoformat()

                # Skip weekends (Saturday=5, Sunday=6)
                if current_date.weekday() >= 5:
                    continue

                for student_subject in student_subjects:
                    # Random attendance (85% present, 15% absent)
                    is_present = random.random() < 0.85
                    status = 1 if is_present else 0

                    # Only create attendance for some subjects each day (not all)
                    if random.random() < 0.3:  # 30% chance for each subject
                        attendance_records.append((
                            student_subject['student_id'],
                            student_subject['class_id'],
                            student_subject['subject_id'],
                            status,
                            date_str,
                            time_str,
                            timestamp
                        ))

            # Insert attendance records
            cursor.executemany("""
                INSERT OR IGNORE INTO presences
                (student_id, class_id, subject_id, status, date, time, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, attendance_records)

            conn.commit()

            print(f"   ✅ Created {len(attendance_records)} attendance records")
            print("✅ Attendance history created successfully")
            return True

    except Exception as e:
        print(f"❌ Failed to create attendance history: {e}")
        return False

def main():
    """Main function to seed the database with Tunisian prep school data."""
    print("🇹🇳 Seeding database with Tunisian prep school data...")
    print("=" * 60)

    # Initialize database structure
    print("🔧 Initializing database structure...")
    initialize_database()
    initialize_quiz_tables()

    # Clear existing data
    if not clear_database():
        print("❌ Failed to clear database. Exiting.")
        return False

    # Create teachers
    teachers = create_teachers()
    if not teachers:
        print("❌ Failed to create teachers. Exiting.")
        return False

    # Create classes and subjects
    if not create_classes_and_subjects():
        print("❌ Failed to create classes and subjects. Exiting.")
        return False

    # Create sample students
    if not create_sample_students():
        print("❌ Failed to create students. Exiting.")
        return False

    # Create sample quizzes
    if not create_sample_quizzes():
        print("❌ Failed to create quizzes. Exiting.")
        return False

    # Create attendance history
    if not create_attendance_history():
        print("❌ Failed to create attendance history. Exiting.")
        return False

    print("=" * 60)
    print("✅ Database seeded successfully with Tunisian prep school data!")
    print("\n📋 Summary:")
    print("   • 5 Teachers created")
    print("   • 5 Classes created (Sciences Exp A&B, Math A&B, Lettres)")
    print("   • 40 Students created (8 per class)")
    print("   • Multiple subjects per class")
    print("   • Sample quizzes created")
    print("   • 30 days of attendance history created")
    print("\n🔑 Login credentials:")
    print("   Admin: username='admin', password='admin'")
    print("   Teachers: username='[teacher_username]', password='teacher123'")
    print("   Example: username='ahmed_ben_salem', password='teacher123'")

    return True

if __name__ == "__main__":
    main()
