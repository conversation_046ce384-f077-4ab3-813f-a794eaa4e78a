#!/usr/bin/env python3
"""
Database clearing utility for the Teacher Assistant application.
This script clears all data from the database while preserving the table structure.
"""

import sys
import os
import sqlite3

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from facial_recognition_system.config import Config

def clear_database():
    """Clear all data from the database while preserving structure."""
    print("🗑️  Clearing database...")
    
    try:
        Config.ensure_directories()
        db_path = Config.get_db_path()
        
        with sqlite3.connect(db_path) as conn:
            conn.execute('PRAGMA foreign_keys = OFF')
            cursor = conn.cursor()
            
            # Get all table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            # Clear all tables
            for table in tables:
                table_name = table[0]
                if table_name != 'sqlite_sequence':  # Don't clear the sequence table
                    cursor.execute(f"DELETE FROM {table_name}")
                    print(f"   ✅ Cleared table: {table_name}")
            
            # Reset auto-increment sequences
            cursor.execute("DELETE FROM sqlite_sequence")
            
            conn.execute('PRAGMA foreign_keys = ON')
            conn.commit()
            
        print("✅ Database cleared successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to clear database: {e}")
        return False

def main():
    """Main function to clear the database."""
    print("🧹 Database Clearing Utility")
    print("=" * 40)
    
    # Ask for confirmation
    response = input("⚠️  This will delete ALL data from the database. Continue? (y/N): ")
    if response.lower() != 'y':
        print("❌ Operation cancelled.")
        return False
    
    if clear_database():
        print("=" * 40)
        print("✅ Database cleared successfully!")
        print("💡 You may want to run the seeding script to populate with sample data:")
        print("   python tools/seed_tunisian_data.py")
    else:
        print("❌ Failed to clear database.")
        return False
    
    return True

if __name__ == "__main__":
    main()
