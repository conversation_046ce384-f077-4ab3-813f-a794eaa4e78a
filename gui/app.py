
import flet as ft
import os
import sys

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.views.start import create_start_view
from gui.views.login import create_login_view
from gui.views.dashboard import create_dashboard_view
from gui.views.admin_dashboard import create_admin_dashboard_view
from gui.views.admin_teachers import create_admin_teachers_view
from gui.views.admin_data import create_admin_data_view
from gui.views.admin_data_detail import (
    create_admin_classes_view, create_admin_subjects_view, create_admin_class_students_view
)
from gui.views.classes import create_classes_view
from gui.views.subjects import create_subjects_view
from gui.views.quizzes import create_quizzes_view
from gui.views.settings import create_settings_view
from gui.views.student_details import create_student_details_view
from gui.state.app_state import AppState
from gui.config.constants import (
    APP_NAME, WINDOW_WIDTH, WINDOW_HEIGHT,
    ROUTE_START, ROUTE_LOGIN, ROUTE_DASHBOARD, ROUTE_ADMIN_DASHBOARD, ROUTE_ADMIN_TEACHERS,
    ROUTE_CLASSES, ROUTE_SUBJECTS, ROUTE_QUIZZES, ROUTE_SETTINGS, ROUTE_STUDENT_DETAILS
)

def main(page: ft.Page):
    page.title = APP_NAME
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 0
    page.fonts = {
        "Roboto": "https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap",
        "Montserrat": "https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&display=swap",
        "Open Sans": "https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap",
        "Poppins": "https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
    }
    page.theme = ft.Theme(font_family="Poppins")
    page.is_mobile = False
    platform = getattr(page, 'platform', None)
    if platform is not None:
        if platform not in [ft.PagePlatform.ANDROID, ft.PagePlatform.IOS]:
            page.window_width = WINDOW_WIDTH
            page.window_height = WINDOW_HEIGHT
        page.is_mobile = platform in [ft.PagePlatform.ANDROID, ft.PagePlatform.IOS]
    page.app_state = AppState(page)
    setup_navigation_and_views(page)
    page.go(ROUTE_LOGIN)

def setup_navigation_and_views(page: ft.Page):
    def route_change(route):
        current_route = getattr(route, 'route', page.route)
        page.views.clear()
        check_connection_fn = getattr(page, 'check_connection', None)
        if check_connection_fn is not None:
            check_connection_fn()

        # Check authentication for protected routes
        current_user = getattr(page.app_state, 'current_user', None)
        protected_routes = [ROUTE_DASHBOARD, ROUTE_ADMIN_DASHBOARD, ROUTE_ADMIN_TEACHERS, "/admin/data",
                          "/admin/classes", "/admin/subjects",
                          ROUTE_CLASSES, ROUTE_SUBJECTS, ROUTE_QUIZZES, ROUTE_SETTINGS, ROUTE_STUDENT_DETAILS]

        if current_route != ROUTE_LOGIN and current_route != ROUTE_START and any(current_route.startswith(route) for route in protected_routes):
            if not current_user:
                page.views.append(create_login_view(page))
                page.update()
                return

        if current_route == ROUTE_LOGIN:
            page.views.append(create_login_view(page))
        elif current_route == ROUTE_START:
            page.views.append(create_start_view(page))
        elif current_route == ROUTE_ADMIN_DASHBOARD:
            page.views.append(create_admin_dashboard_view(page))
        elif current_route == ROUTE_ADMIN_TEACHERS:
            page.views.append(create_admin_teachers_view(page))
        elif current_route == "/admin/data":
            page.views.append(create_admin_data_view(page))
        elif current_route == "/admin/classes":
            page.views.append(create_admin_classes_view(page))
        elif current_route.startswith("/admin/classes/") and current_route.endswith("/students"):
            # Extract class ID from route like "/admin/classes/123/students"
            try:
                class_id = int(current_route.split('/')[-2])
                page.views.append(create_admin_class_students_view(page, class_id))
            except (ValueError, IndexError):
                page.views.append(create_admin_classes_view(page))
        elif current_route == "/admin/subjects":
            page.views.append(create_admin_subjects_view(page))
        elif current_route == ROUTE_DASHBOARD:
            page.views.append(create_dashboard_view(page))
        elif current_route == ROUTE_CLASSES:
            page.views.append(create_classes_view(page))
        elif current_route == ROUTE_SUBJECTS:
            page.views.append(create_subjects_view(page))
        elif current_route == ROUTE_QUIZZES:
            page.views.append(create_quizzes_view(page))
        elif current_route == ROUTE_SETTINGS:
            page.views.append(create_settings_view(page))
        elif current_route.startswith(ROUTE_STUDENT_DETAILS):
            # Extract student ID from route like "/student/123"
            try:
                student_id = int(current_route.split('/')[-1])
                page.views.append(create_student_details_view(page, student_id))
            except (ValueError, IndexError):
                page.views.append(create_classes_view(page))
        else:
            page.views.append(create_login_view(page))
        page.update()

    def view_pop(_):
        check_connection_fn = getattr(page, 'check_connection', None)
        if check_connection_fn is not None:
            check_connection_fn()
        page.views.pop()
        top_view = page.views[-1]
        page.go(top_view.route)

    page.on_route_change = route_change
    page.on_view_pop = view_pop
    page.go(page.route)

if __name__ == "__main__":
    ft.app(target=main)
