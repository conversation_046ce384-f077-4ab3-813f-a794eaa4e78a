import socket
import threading
import time
from gui.utils.network import get_local_ip
from facial_recognition_system.config import Config

_video_streaming_server_process = None
_video_streaming_server_running = False
_video_streaming_server_port = Config.VIDEO_STREAM_PORT

def is_port_in_use(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def find_available_port(start_port=None, max_attempts=10):
    if start_port is None:
        start_port = Config.VIDEO_STREAM_PORT
    port = start_port
    attempts = 0
    while attempts < max_attempts:
        if not is_port_in_use(port):
            return port
        port += 1
        attempts += 1
    return start_port

def is_video_streaming_server_running():
    global _video_streaming_server_running
    return _video_streaming_server_running

def get_video_streaming_server_port():
    global _video_streaming_server_port
    print(f"[VideoStream] Returning video streaming server port: {_video_streaming_server_port}")
    return _video_streaming_server_port

def start_video_streaming_server():
    global _video_streaming_server_process, _video_streaming_server_running, _video_streaming_server_port
    if _video_streaming_server_running:
        print("[VideoStream] Video streaming server already running.")
        return True, _video_streaming_server_port
    _video_streaming_server_port = find_available_port()
    print(f"[VideoStream] Attempting to start video streaming server on port {_video_streaming_server_port}")
    try:
        from gui.services.video_streaming_service import run_server
        server_thread = threading.Thread(
            target=run_server,
            args=('0.0.0.0', _video_streaming_server_port),
            daemon=True
        )
        server_thread.start()
        time.sleep(1)
        if is_port_in_use(_video_streaming_server_port):
            _video_streaming_server_running = True
            print(f"[VideoStream] Video streaming server started successfully on port {_video_streaming_server_port}")
            return True, _video_streaming_server_port
        else:
            print("[VideoStream] Failed to start video streaming server: port not in use after start.")
            return False, None
    except Exception as e:
        print(f"[VideoStream] Exception while starting video streaming server: {e}")
        return False, None

def stop_video_streaming_server():
    global _video_streaming_server_running, _video_streaming_server_process
    if not _video_streaming_server_running:
        print("[VideoStream] Video streaming server is not running.")
        return True
    try:
        import requests
        print(f"[VideoStream] Sending stop request to server on port {_video_streaming_server_port}")
        requests.post(f"http://localhost:{_video_streaming_server_port}/stop_stream")
        _video_streaming_server_running = False
        print("[VideoStream] Video streaming server stopped successfully.")
        return True
    except Exception as e:
        print(f"[VideoStream] Exception while stopping video streaming server: {e}")
        return False

def generate_video_stream_url(class_id, class_name, subject_id=None, subject_name=None):
    success, port = start_video_streaming_server()
    if not success:
        return None
    local_ip = get_local_ip()
    url = f"http://{local_ip}:{port}/stream_page?class_id={class_id}&class_name={class_name}"
    if subject_id:
        url += f"&subject_id={subject_id}"
    if subject_name:
        url += f"&subject_name={subject_name}"
    return url
